<?php

namespace Database\Seeders;

<<<<<<< HEAD
use App\Models\Country;
use App\Models\Package;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;
=======
use App\Models\City;
use App\Models\Country;
use App\Models\Package;
use App\Models\Region;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Log;
>>>>>>> 37ec79479d079c3c6401c77abe9028c132ff17c8

class CitiesPackagesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // first, empty the tables
        DB::table('city_package')->delete();

        $json = File::get(database_path('seeders/data/cities_packages.json'));
        $packages = json_decode($json, true);

        echo "Total packages: " . count($packages) . PHP_EOL;

        // manual hacks
        // netherlands
//        $netherlands = Country::where('name', 'Ολλανδία, Βασίλειο του')->first();
//        if (!empty($netherlands))
//        {
//            $netherlands->update(['name' => 'Ολλανδία']);
//            echo 'Netherlands fixed' . PHP_EOL . "\n";
//        }

        foreach ($packages as $entry)
        {
            // Skip if essential values are missing
            // check fer package id
            if ( empty($entry['package_id']) )
            {
                echo "Missing package id" . PHP_EOL . "\n";
                continue;
            }

            if (
                empty($entry['cities'])
            )
            {
                echo "Missing values for " . $entry['package_id'] . PHP_EOL . "\n";
<<<<<<< HEAD
=======
                Log::info("Missing values for " . $entry['package_id']);
>>>>>>> 37ec79479d079c3c6401c77abe9028c132ff17c8
                continue;
            }

            // fetch package
            $package = Package::where('id', $entry['package_id'])->first();

            if (empty($package))
            {
                echo "Package not found: " . $entry['package_id'] . PHP_EOL . "\n";
<<<<<<< HEAD
=======
                Log::info("Package not found: " . $entry['package_id']);
>>>>>>> 37ec79479d079c3c6401c77abe9028c132ff17c8
                continue;
            }

            // loop through all cities of package
            foreach ($entry['cities'] as $city)
            {
                // check if city exists
<<<<<<< HEAD
                $cityId = DB::table('cities')->where('name', $city['city'])->first();

                if (empty($cityId))
                {
                    echo "City not found: " . $city['city'] . PHP_EOL . "\n";
                    continue;
=======
                $city_model = DB::table('cities')->where('name', $city['city'])->first();

                if (empty($city_model))
                {
                    // let's try to insert it
                    // first look for region
                    $region_model = DB::table('regions')->where('name', $city['region'])->first();

                    // if region not found
                    if (empty($region_model))
                    {
                        // let's try to insert it
                        // first look for country
                        $country_model = DB::table('countries')->where('name', $city['country'])->first();

                        // if country not found, log und skip
                        if (empty($country_model))
                        {
                            Log::info("City not found: " . $city['city']);
                            Log::info("Region not found: " . $city['region']);
                            Log::info("Country not found: " . $city['country']);

                            continue;
                        }

                        // we haz country
                        // insert region
                        $region_model = Region::updateOrCreate(
                            [
                                'name' => $city['region'],
                                'country_id' => $country_model->id,
                            ],
                            [
                                'name' => $city['region'],
                                'country_id' => $country_model->id,
                                'created_at' => now(),
                                'updated_at' => now(),
                            ]
                        );

                        echo "Region created: " . $city['region'] . PHP_EOL . "\n";
                        Log::info("Region created: " . $city['region']);
                    }

                    // we haz region
                    // insert city
                    $city_model = City::updateOrCreate(
                        [
                            'name' => $city['city'],
                            'region_id' => $region_model->id,
                        ],
                        [
                            'name' => $city['city'],
                            'region_id' => $region_model->id,
                            'created_at' => now(),
                            'updated_at' => now(),
                        ]
                    );

                    echo "City created: " . $city_model->name . PHP_EOL . "\n";
                    Log::info("City created: " . $city_model->name);
>>>>>>> 37ec79479d079c3c6401c77abe9028c132ff17c8
                }

                // attach city to package
                DB::table('city_package')->insert([
<<<<<<< HEAD
                    'city_id' => $cityId->id,
=======
                    'city_id' => $city_model->id,
>>>>>>> 37ec79479d079c3c6401c77abe9028c132ff17c8
                    'package_id' => $entry['package_id'],
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
                echo "City attached: " . $city['city'] . " to package (id: " . $entry['package_id'] . ") - " . $package->title . PHP_EOL . "\n";
            }
        }
    }
}
